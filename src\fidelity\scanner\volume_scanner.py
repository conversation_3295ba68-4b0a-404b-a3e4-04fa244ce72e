# volume_scanner.py - Scans stocks based on volume thresholds during trading day

import requests
import json
from typing import List, Dict
from fidelity.common.api_headers import get_fidelity_headers, get_fidelity_headers_v2 # Updated import to new file name
import pandas as pd
from datetime import datetime
from fidelity.scanner.stock_screener import intra_screen # Updated import to new file name

from dotenv import load_dotenv
import os

load_dotenv()

print("Running scan on file:", os.getenv( "FIDELITY_FILE"))  # Ensure the environment variable is set

import pandas as pd
from typing import List

def get_csv_stock_list_from_excel(file_path: str) -> List[str]:
    df = pd.read_excel(file_path, engine='openpyxl')

    # Check for required columns
    if 'Symbol' not in df.columns or 'TTM' not in df.columns:
        raise ValueError("The Excel file does not contain the required 'Symbol' or 'TTM' columns")

    # Filter by TTM being True
    #df = df[df['TTM'] == True]

    # Extract symbols as a list
    symbol_list = df['Symbol'].astype(str).tolist()

    return symbol_list

def chunk_list(data: List[str], chunk_size: int) -> List[List[str]]:
    return [data[i:i + chunk_size] for i in range(0, len(data), chunk_size)]


def get_volume_percentage_threshold(current_time: datetime) -> float:
    """Determine the rvol threshold based on the current time."""
    # Extract hour and minute for clarity
    hour, minute = current_time.hour, current_time.minute

    # Define intervals explicitly
    if (hour == 9 and minute >= 30) or (hour == 10 and minute < 30):
        return 25  # 9:30 AM - 10:30 AM
    elif (hour == 10 and minute >= 30) or (hour == 11) or (hour == 12 and minute < 30):
        return 40  # 10:30 AM - 12:30 PM
    elif (hour == 12 and minute >= 30) or (hour == 13) or (hour == 14 and minute < 30):
        return 60  # 12:30 PM - 2:30 PM
    elif (hour == 14 and minute >= 30) or (hour == 15) or (hour == 16 and minute < 0):
        return 80  # 2:30 PM - 4:00 PM
    else:
        return 100  # Outside defined intervals

def filter_and_sort_symbols(response_data: Dict, volume_percentage: float) -> str:
    qualified_symbols = []
    for data in response_data['quoteData']:
        pctChgToday = float(data['pctChgToday'].strip('%')) / 100
        volume = float(data['volume'].replace(',', ''))
        avgVol10Day = float(data['avgVol10Day'].replace(',', ''))

        if pctChgToday > 0 and volume >= volume_percentage/100 * avgVol10Day:
            ratio = volume / avgVol10Day
            qualified_symbols.append((data['symbol'], ratio))

    qualified_symbols.sort(key=lambda x: x[1], reverse=True)

    return  [symbol for symbol, _ in qualified_symbols]

# Example usage:
file_path = os.getenv('FIDELITY_FILE')
symbol_list = get_csv_stock_list_from_excel(file_path)

max_symbols_per_request = 50
symbol_chunks = chunk_list(symbol_list, max_symbols_per_request)
# Determine the current time and set the volume percentage
current_time = datetime.now()
volume_percentage_threshold = get_volume_percentage_threshold(current_time)

# print current_time and volume_percentage_threshold
print("Current Time:", current_time.strftime("%Y-%m-%d %H:%M:%S"), "Volume Percentage Threshold:", volume_percentage_threshold)

all_symbols = []


for symbols in symbol_chunks:
    symbols_string = ','.join(symbols)
    url = "https://digital.fidelity.com/prgw/digital/research/api/quote"

    payload = json.dumps({
        "symbol": symbols_string
    })

    headers = get_fidelity_headers_v2()

    response = requests.post(url, headers=headers, data=payload)


    #print(response.text)

    try:
        response_data = response.json()
        filtered_symbols = filter_and_sort_symbols(response_data, volume_percentage_threshold)
        #print(filtered_symbols)
        #check empty
        if filtered_symbols is not None:
         all_symbols =  filtered_symbols  + all_symbols
         #print(filtered_symbols)
    except (ValueError, KeyError) as e:
        print("Failed to parse response JSON or missing expected fields:", e)
        raise e

#if(input("Add Intra screen option Y/N: ")) == 'Y':
all_symbols = all_symbols + intra_screen( 1 + (volume_percentage_threshold/100))


all_symbols = list(dict.fromkeys(all_symbols))

# Print the present symbols
print(all_symbols)

# split this into comma separated string with 20 values max in
chunks = [",".join(all_symbols[i:i+30]) for i in range(0, len(all_symbols), 30)]
for chunk in chunks:
 print(chunk)
